CREATE DATABASE  IF NOT EXISTS `deportes_sm` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `deportes_sm`;
-- MySQL dump 10.13  Distrib 8.0.28, for Win64 (x86_64)
--
-- Host: localhost    Database: deportes_sm
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `asistencia`
--

DROP TABLE IF EXISTS `asistencia`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asistencia` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reserva_id` int NOT NULL,
  `registrado_por` int NOT NULL,
  `asistentes_reales` int DEFAULT NULL,
  `hora_entrada` time DEFAULT NULL,
  `hora_salida` time DEFAULT NULL,
  `observaciones` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `reserva_id` (`reserva_id`),
  KEY `registrado_por` (`registrado_por`),
  CONSTRAINT `asistencia_ibfk_1` FOREIGN KEY (`reserva_id`) REFERENCES `reservas` (`id`),
  CONSTRAINT `asistencia_ibfk_2` FOREIGN KEY (`registrado_por`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `asistencia`
--

LOCK TABLES `asistencia` WRITE;
/*!40000 ALTER TABLE `asistencia` DISABLE KEYS */;
/*!40000 ALTER TABLE `asistencia` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `asistencias_coordinadores`
--

DROP TABLE IF EXISTS `asistencias_coordinadores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `asistencias_coordinadores` (
  `id` int NOT NULL AUTO_INCREMENT,
  `coordinador_id` int NOT NULL,
  `instalacion_id` int NOT NULL,
  `fecha` date NOT NULL,
  `hora_programada_inicio` time NOT NULL,
  `hora_programada_fin` time NOT NULL,
  `hora_entrada` time DEFAULT NULL,
  `estado_entrada` enum('a-tiempo','tarde','no-asistio','pendiente') DEFAULT 'pendiente',
  `hora_salida` time DEFAULT NULL,
  `estado_salida` enum('a-tiempo','tarde','no-asistio','pendiente') DEFAULT 'pendiente',
  `ubicacion` varchar(255) DEFAULT NULL,
  `notas` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_asistencia_coordinador_idx` (`coordinador_id`),
  KEY `fk_asistencia_instalacion_idx` (`instalacion_id`),
  KEY `idx_asistencias_fecha` (`fecha`),
  CONSTRAINT `fk_asistencia_coordinador` FOREIGN KEY (`coordinador_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_asistencia_instalacion` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `asistencias_coordinadores`
--

LOCK TABLES `asistencias_coordinadores` WRITE;
/*!40000 ALTER TABLE `asistencias_coordinadores` DISABLE KEYS */;
INSERT INTO `asistencias_coordinadores` VALUES (1,4,2,'2025-05-09','08:00:00','12:00:00','07:55:00','a-tiempo','12:05:00','a-tiempo','Parque Juan Pablo II',NULL,'2025-05-09 12:55:00','2025-05-09 17:05:00'),(2,4,2,'2025-05-08','08:00:00','12:00:00','08:10:00','tarde','11:45:00','a-tiempo','Parque Juan Pablo II',NULL,'2025-05-08 13:10:00','2025-05-08 16:45:00'),(3,5,1,'2025-05-11','08:00:00','12:00:00','08:05:00','a-tiempo',NULL,'pendiente','Complejo Deportivo Este',NULL,'2025-05-11 13:05:00','2025-05-11 13:05:00'),(4,5,1,'2025-05-07','08:00:00','12:00:00','08:00:00','a-tiempo','12:00:00','a-tiempo','Complejo Deportivo Municipal',NULL,'2025-05-07 13:00:00','2025-05-07 17:00:00'),(5,5,1,'2025-05-06','08:00:00','12:00:00',NULL,'no-asistio',NULL,'no-asistio','Complejo Deportivo Municipal','No se presentó por motivos de salud','2025-05-06 17:30:00','2025-05-06 17:30:00'),(6,8,6,'2025-06-07','20:00:00','21:00:00','19:47:00','a-tiempo','19:49:00','a-tiempo','Iquique 110',NULL,'2025-06-08 00:47:54','2025-06-08 00:49:23');
/*!40000 ALTER TABLE `asistencias_coordinadores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `bloqueos_temporales`
--

DROP TABLE IF EXISTS `bloqueos_temporales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bloqueos_temporales` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `usuario_id` int NOT NULL,
  `fecha` date NOT NULL,
  `hora_inicio` time NOT NULL,
  `hora_fin` time NOT NULL,
  `token` varchar(255) NOT NULL,
  `expiracion` timestamp NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_bloqueo_instalacion_idx` (`instalacion_id`),
  KEY `fk_bloqueo_usuario_idx` (`usuario_id`),
  CONSTRAINT `fk_bloqueo_instalacion` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bloqueo_usuario` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `bloqueos_temporales`
--

LOCK TABLES `bloqueos_temporales` WRITE;
/*!40000 ALTER TABLE `bloqueos_temporales` DISABLE KEYS */;
/*!40000 ALTER TABLE `bloqueos_temporales` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `caracteristicas_instalacion`
--

DROP TABLE IF EXISTS `caracteristicas_instalacion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `caracteristicas_instalacion` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `descripcion` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `caracteristicas_instalacion_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `caracteristicas_instalacion`
--

LOCK TABLES `caracteristicas_instalacion` WRITE;
/*!40000 ALTER TABLE `caracteristicas_instalacion` DISABLE KEYS */;
INSERT INTO `caracteristicas_instalacion` VALUES (1,1,'Dimensiones: 25m x 12.5m','2025-05-21 03:33:21','2025-05-21 03:33:21'),(2,1,'Profundidad: 1.5m - 2.2m','2025-05-21 03:33:21','2025-05-21 03:33:21'),(3,1,'Temperatura del agua controlada','2025-05-21 03:33:21','2025-05-21 03:33:21'),(4,1,'Iluminación LED','2025-05-21 03:33:21','2025-05-21 03:33:21'),(5,2,'Dimensiones: 90m x 45m','2025-05-21 03:33:21','2025-05-21 03:33:21'),(6,2,'Grass sintético de alta calidad','2025-05-21 03:33:21','2025-05-21 03:33:21'),(7,2,'Líneas reglamentarias','2025-05-21 03:33:21','2025-05-21 03:33:21'),(8,2,'Sistema de drenaje','2025-05-21 03:33:21','2025-05-21 03:33:21'),(9,6,'x','2025-06-08 00:34:11','2025-06-08 00:34:11');
/*!40000 ALTER TABLE `caracteristicas_instalacion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comodidades_instalacion`
--

DROP TABLE IF EXISTS `comodidades_instalacion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comodidades_instalacion` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `descripcion` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `comodidades_instalacion_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comodidades_instalacion`
--

LOCK TABLES `comodidades_instalacion` WRITE;
/*!40000 ALTER TABLE `comodidades_instalacion` DISABLE KEYS */;
INSERT INTO `comodidades_instalacion` VALUES (1,1,'Vestuarios con casilleros','2025-05-21 03:33:21','2025-05-21 03:33:21'),(2,1,'Duchas con agua caliente','2025-05-21 03:33:21','2025-05-21 03:33:21'),(3,1,'Área de descanso','2025-05-21 03:33:21','2025-05-21 03:33:21'),(4,2,'Bancas para reservas','2025-05-21 03:33:21','2025-05-21 03:33:21'),(5,2,'Iluminación nocturna','2025-05-21 03:33:21','2025-05-21 03:33:21'),(6,2,'Estacionamiento cercano','2025-05-21 03:33:21','2025-05-21 03:33:21'),(7,6,'x','2025-06-08 00:34:11','2025-06-08 00:34:11');
/*!40000 ALTER TABLE `comodidades_instalacion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `configuracion_general`
--

DROP TABLE IF EXISTS `configuracion_general`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `configuracion_general` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre_sitio` varchar(255) NOT NULL,
  `descripcion_sitio` text,
  `telefono_contacto` varchar(50) DEFAULT NULL,
  `email_contacto` varchar(255) DEFAULT NULL,
  `max_reservas_por_usuario` int DEFAULT '3',
  `limite_tiempo_cancelacion` int DEFAULT '48',
  `modo_mantenimiento` tinyint(1) DEFAULT '0',
  `registro_habilitado` tinyint(1) DEFAULT '1',
  `reservas_habilitadas` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `configuracion_general`
--

LOCK TABLES `configuracion_general` WRITE;
/*!40000 ALTER TABLE `configuracion_general` DISABLE KEYS */;
INSERT INTO `configuracion_general` VALUES (1,'DeporSM','Sistema de reserva de canchas y servicios deportivos para la Municipalidad de San Miguel.','987-654-321','<EMAIL>',3,48,0,1,1,'2025-06-08 00:09:19','2025-06-08 05:48:52');
/*!40000 ALTER TABLE `configuracion_general` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `configuracion_seguridad`
--

DROP TABLE IF EXISTS `configuracion_seguridad`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `configuracion_seguridad` (
  `id` int NOT NULL AUTO_INCREMENT,
  `two_factor_auth` tinyint(1) DEFAULT '1',
  `password_expiration` tinyint(1) DEFAULT '1',
  `password_expiration_days` int DEFAULT '90',
  `min_password_length` int DEFAULT '8',
  `require_special_chars` tinyint(1) DEFAULT '1',
  `require_numbers` tinyint(1) DEFAULT '1',
  `require_uppercase` tinyint(1) DEFAULT '1',
  `max_login_attempts` int DEFAULT '5',
  `lockout_duration` int DEFAULT '30',
  `session_timeout` int DEFAULT '60',
  `ip_restriction` tinyint(1) DEFAULT '0',
  `allowed_ips` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `configuracion_seguridad`
--

LOCK TABLES `configuracion_seguridad` WRITE;
/*!40000 ALTER TABLE `configuracion_seguridad` DISABLE KEYS */;
INSERT INTO `configuracion_seguridad` VALUES (1,1,1,90,8,1,1,1,5,30,60,0,NULL,'2025-04-24 14:07:15','2025-04-24 14:07:15');
/*!40000 ALTER TABLE `configuracion_seguridad` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `configuracion_sistema`
--

DROP TABLE IF EXISTS `configuracion_sistema`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `configuracion_sistema` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre_parametro` varchar(100) NOT NULL,
  `valor` text,
  `tipo` varchar(50) NOT NULL,
  `descripcion` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nombre_parametro` (`nombre_parametro`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `configuracion_sistema`
--

LOCK TABLES `configuracion_sistema` WRITE;
/*!40000 ALTER TABLE `configuracion_sistema` DISABLE KEYS */;
INSERT INTO `configuracion_sistema` VALUES (1,'nombre_sistema','DeporSM','string','Nombre del sistema','2025-04-24 14:07:15','2025-04-24 14:07:15'),(2,'logo_url','/placeholder-logo.svg','string','URL del logotipo del sistema','2025-04-24 14:07:15','2025-04-24 14:07:15'),(3,'email_contacto','<EMAIL>','string','Email de contacto','2025-04-24 14:07:15','2025-04-24 14:07:15'),(4,'tiempo_anticipacion_reserva','2','integer','Días de anticipación para realizar una reserva','2025-04-24 14:07:15','2025-04-24 14:07:15'),(5,'max_reservas_semana_vecino','3','integer','Cantidad máxima de reservas por semana para vecinos','2025-04-24 14:07:15','2025-04-24 14:07:15'),(6,'version_sistema','1.0.0','string','Versión actual del sistema','2025-04-24 14:07:15','2025-04-24 14:07:15');
/*!40000 ALTER TABLE `configuracion_sistema` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `coordinadores_instalaciones`
--

DROP TABLE IF EXISTS `coordinadores_instalaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coordinadores_instalaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `usuario_id` int NOT NULL,
  `instalacion_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_coordinador_instalacion` (`usuario_id`,`instalacion_id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `coordinadores_instalaciones_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE,
  CONSTRAINT `coordinadores_instalaciones_ibfk_2` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coordinadores_instalaciones`
--

LOCK TABLES `coordinadores_instalaciones` WRITE;
/*!40000 ALTER TABLE `coordinadores_instalaciones` DISABLE KEYS */;
INSERT INTO `coordinadores_instalaciones` VALUES (1,4,1,'2025-04-28 14:02:13','2025-04-28 14:02:13'),(2,4,2,'2025-04-28 14:08:00','2025-04-28 14:08:00'),(3,5,1,'2025-04-28 14:45:36','2025-04-28 14:45:36'),(4,4,3,'2025-04-28 14:45:36','2025-04-28 14:45:36'),(5,8,6,'2025-06-08 00:37:45','2025-06-08 00:37:45');
/*!40000 ALTER TABLE `coordinadores_instalaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `horarios_coordinadores`
--

DROP TABLE IF EXISTS `horarios_coordinadores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `horarios_coordinadores` (
  `id` int NOT NULL AUTO_INCREMENT,
  `coordinador_instalacion_id` int NOT NULL,
  `dia_semana` enum('lunes','martes','miercoles','jueves','viernes','sabado','domingo') NOT NULL,
  `hora_inicio` time NOT NULL,
  `hora_fin` time NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `coordinador_instalacion_id` (`coordinador_instalacion_id`),
  CONSTRAINT `horarios_coordinadores_ibfk_1` FOREIGN KEY (`coordinador_instalacion_id`) REFERENCES `coordinadores_instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `horarios_coordinadores`
--

LOCK TABLES `horarios_coordinadores` WRITE;
/*!40000 ALTER TABLE `horarios_coordinadores` DISABLE KEYS */;
INSERT INTO `horarios_coordinadores` VALUES (1,1,'lunes','08:00:00','12:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(2,1,'martes','08:00:00','12:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(3,1,'miercoles','14:00:00','18:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(4,1,'jueves','08:00:00','12:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(5,1,'viernes','14:00:00','18:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(6,2,'lunes','14:00:00','18:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(7,2,'miercoles','08:00:00','12:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(8,2,'viernes','08:00:00','12:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(9,3,'martes','14:00:00','18:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(10,3,'jueves','14:00:00','18:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(11,4,'sabado','09:00:00','13:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(12,4,'domingo','09:00:00','13:00:00','2025-05-08 19:00:00','2025-05-08 19:00:00'),(13,5,'sabado','20:00:00','21:00:00','2025-06-08 00:37:45','2025-06-08 00:37:45');
/*!40000 ALTER TABLE `horarios_coordinadores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `horarios_disponibles`
--

DROP TABLE IF EXISTS `horarios_disponibles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `horarios_disponibles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `dia_semana` enum('LUNES','MARTES','MIERCOLES','JUEVES','VIERNES','SABADO','DOMINGO') NOT NULL,
  `hora_inicio` time NOT NULL,
  `hora_fin` time NOT NULL,
  `disponible` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `horarios_disponibles_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `horarios_disponibles`
--

LOCK TABLES `horarios_disponibles` WRITE;
/*!40000 ALTER TABLE `horarios_disponibles` DISABLE KEYS */;
INSERT INTO `horarios_disponibles` VALUES (1,1,'LUNES','08:00:00','10:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(2,1,'LUNES','10:00:00','12:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(3,1,'LUNES','14:00:00','16:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(4,1,'LUNES','16:00:00','18:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(5,1,'MARTES','08:00:00','10:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(6,1,'MARTES','10:00:00','12:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(7,1,'MARTES','14:00:00','16:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(8,1,'MARTES','16:00:00','18:00:00',1,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(16,6,'MARTES','08:00:00','10:00:00',1,'2025-06-08 00:49:07','2025-06-08 00:49:07'),(17,4,'LUNES','08:00:00','10:00:00',1,'2025-06-08 03:27:30','2025-06-08 03:27:30');
/*!40000 ALTER TABLE `horarios_disponibles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `instalaciones`
--

DROP TABLE IF EXISTS `instalaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `instalaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre` varchar(100) NOT NULL,
  `descripcion` text,
  `ubicacion` varchar(255) DEFAULT NULL,
  `latitud` double DEFAULT NULL,
  `longitud` double DEFAULT NULL,
  `radio_validacion` int DEFAULT '100',
  `tipo` varchar(50) DEFAULT NULL,
  `capacidad` int DEFAULT NULL,
  `imagen_url` varchar(255) DEFAULT NULL,
  `activo` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `precio` decimal(10,2) NOT NULL DEFAULT '0.00',
  `contacto_numero` varchar(20) DEFAULT NULL,
  `requiere_mantenimiento` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `instalaciones`
--

LOCK TABLES `instalaciones` WRITE;
/*!40000 ALTER TABLE `instalaciones` DISABLE KEYS */;
INSERT INTO `instalaciones` VALUES (1,'Piscina Olímpica','Piscina olímpica con 8 carriles y profundidad de 2 metros. Ideal para entrenamiento y competencias.','Complejo Deportivo Municipal',-12.0776,-77.0919,150,'piscina',50,'/images/Piscina_Olímpica_1.jpg',1,'2025-05-21 03:33:21','2025-05-21 03:33:21',15.00,'987-654-321',1),(2,'Cancha de Fútbol Principal','Cancha de fútbol de grass natural con medidas reglamentarias. Incluye tribunas para espectadores.','Estadio Municipal',-12.0756,-77.0889,200,'cancha',22,'/images/Cancha_Futbol_Principal_2.jpeg',1,'2025-05-21 03:33:21','2025-05-21 03:33:21',120.00,'987-654-322',0),(3,'Gimnasio Municipal','Gimnasio completamente equipado con máquinas de cardio, pesas y área de entrenamiento funcional.','Centro Deportivo San Miguel',-12.0796,-77.0939,100,'gimnasio',40,'/images/Gimnasio_Municipal_3.jpeg',1,'2025-05-21 03:33:21','2025-05-21 03:33:21',10.00,'987-654-323',0),(4,'Cancha Multideportiva','Cancha de loza para práctica de básquet, vóley y futsal. Incluye iluminación nocturna.','Parque Deportivo Las Flores',-12.0736,-77.0869,120,'cancha',14,'/images/Cancha_Multideportiva_4.png',1,'2025-05-21 03:33:21','2025-06-08 03:27:30',80.00,'987-654-324',0),(5,'Pista de Atletismo','Pista de atletismo de 400 metros con 8 carriles. Superficie sintética de alta calidad.','Complejo Deportivo Municipal',-12.0786,-77.0909,180,'pista-atletismo',30,'/images/Pista_Atletismo_5.jpeg',1,'2025-05-21 03:33:21','2025-05-21 03:33:21',50.00,'987-654-325',0),(6,'Prueba','xd','Iquique 110',-12.0520351,-77.0440796,100,'cancha-futbol-loza',77,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/instalaciones/instalaciones/1749342849904_EstadioLolo.jpg',1,'2025-06-08 00:34:11','2025-06-08 00:49:07',11.00,'966564107',0);
/*!40000 ALTER TABLE `instalaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `log_actividades`
--

DROP TABLE IF EXISTS `log_actividades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `log_actividades` (
  `id` int NOT NULL AUTO_INCREMENT,
  `usuario_id` int DEFAULT NULL,
  `accion` varchar(100) NOT NULL,
  `recurso` varchar(100) DEFAULT NULL,
  `detalles` text,
  `ip_address` varchar(50) DEFAULT NULL,
  `device` varchar(100) DEFAULT NULL,
  `estado` enum('success','failed') DEFAULT 'success',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `log_actividades_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `log_actividades`
--

LOCK TABLES `log_actividades` WRITE;
/*!40000 ALTER TABLE `log_actividades` DISABLE KEYS */;
/*!40000 ALTER TABLE `log_actividades` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mantenimiento_instalaciones`
--

DROP TABLE IF EXISTS `mantenimiento_instalaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mantenimiento_instalaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `fecha_inicio` datetime NOT NULL,
  `fecha_fin` datetime NOT NULL,
  `motivo` varchar(255) NOT NULL,
  `tipo` varchar(50) DEFAULT 'correctivo',
  `descripcion` text,
  `estado` varchar(50) DEFAULT 'programado',
  `afecta_disponibilidad` tinyint(1) DEFAULT '0',
  `registrado_por` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  KEY `registrado_por` (`registrado_por`),
  CONSTRAINT `mantenimiento_instalaciones_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`),
  CONSTRAINT `mantenimiento_instalaciones_ibfk_2` FOREIGN KEY (`registrado_por`) REFERENCES `usuarios` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mantenimiento_instalaciones`
--

LOCK TABLES `mantenimiento_instalaciones` WRITE;
/*!40000 ALTER TABLE `mantenimiento_instalaciones` DISABLE KEYS */;
INSERT INTO `mantenimiento_instalaciones` VALUES (1,1,'2025-04-01 08:00:00','2025-04-03 18:00:00','Limpieza de filtros','preventivo','Mantenimiento preventivo en la piscina principal.','completado',0,1,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(2,2,'2025-04-05 09:00:00','2025-04-07 17:00:00','Reparación del riego','correctivo','Corrección del sistema automático de riego dañado.','completado',0,1,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(3,3,'2025-05-10 10:00:00','2025-05-15 16:00:00','Cambio de luminarias','preventivo','Instalación de luces LED en el gimnasio.','completado',0,1,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(4,4,'2025-04-20 07:00:00','2025-04-25 16:00:00','Grietas en la cancha','correctivo','Reparación de grietas en la superficie de concreto.','completado',0,1,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(5,5,'2025-06-01 08:00:00','2025-06-02 14:00:00','Nuevo trazado de carriles','preventivo','Mejora del señalamiento en la pista de atletismo.','completado',0,1,'2025-04-25 02:13:13','2025-06-08 00:22:32'),(6,1,'2025-03-10 09:00:00','2025-03-12 18:00:00','Fugas detectadas','correctivo','Reparación urgente por fugas de agua detectadas.','completado',0,2,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(7,2,'2025-05-01 09:00:00','2025-05-03 15:00:00','Mantenimiento rutinario','preventivo','Revisión general del césped.','completado',0,2,'2025-04-25 02:13:13','2025-04-25 02:13:13'),(8,4,'2025-03-01 07:00:00','2025-03-05 16:00:00','Pintura y señalización','correctivo','Renovación de pintura en líneas de juego.','completado',0,2,'2025-04-25 02:13:13','2025-04-25 02:13:13');
/*!40000 ALTER TABLE `mantenimiento_instalaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notificaciones`
--

DROP TABLE IF EXISTS `notificaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notificaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `titulo` varchar(255) NOT NULL,
  `mensaje` text NOT NULL,
  `tipo` varchar(50) NOT NULL,
  `leida` tinyint(1) DEFAULT '0',
  `fecha_envio` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `categoria` varchar(100) DEFAULT NULL,
  `feedback` text,
  `usuario_id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_notificaciones_usuario` (`usuario_id`),
  KEY `idx_notificaciones_leida` (`leida`),
  KEY `idx_notificaciones_fecha` (`fecha_envio`),
  CONSTRAINT `notificaciones_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notificaciones`
--

LOCK TABLES `notificaciones` WRITE;
/*!40000 ALTER TABLE `notificaciones` DISABLE KEYS */;
INSERT INTO `notificaciones` VALUES (1,'Nueva instalación asignada','Se te ha asignado la supervisión de Prueba. Revisa tus horarios y responsabilidades en la sección de instalaciones.','asignacion',0,'2025-06-08 00:37:45',NULL,NULL,8),(33,'Reserva Pendiente','Tu reserva para Prueba (08:00 - 10:00 el 24/06/25) está pendiente de pago.','reserva',0,'2025-06-08 01:59:52',NULL,NULL,7),(39,'Reserva confirmada','Tu reserva para Prueba el día 2025-06-24 de 08:00 a 10:00 ha sido confirmada.','reserva',0,'2025-06-08 02:10:51','confirmacion',NULL,7),(49,'Reserva Pendiente','Tu reserva para Prueba (08:00 - 10:00 el 22/07/25) está pendiente de pago.','reserva',0,'2025-06-08 02:35:26',NULL,NULL,7),(50,'Reserva confirmada','Tu reserva para Prueba el día 2025-07-22 de 08:00 a 10:00 ha sido confirmada.','reserva',0,'2025-06-08 02:35:43','confirmacion',NULL,7),(53,'Reserva cancelada','Tu reserva para Prueba el día 2025-07-22 de 08:00 a 10:00 ha sido cancelada.','reserva',0,'2025-06-08 02:37:42','cancelacion',NULL,7),(54,'Reserva Cancelada','Tu reserva para Prueba (08:00 - 10:00 el martes 22 de julio de 2025) ha sido cancelada.','reserva',0,'2025-06-08 02:37:42',NULL,NULL,7),(63,'Reserva cancelada','Tu reserva para Prueba el día 2025-06-24 de 08:00 a 10:00 ha sido cancelada.','reserva',0,'2025-06-08 03:21:23','cancelacion',NULL,7),(64,'Reserva Cancelada','Tu reserva para Prueba (08:00 - 10:00 el martes 24 de junio de 2025) ha sido cancelada.','reserva',0,'2025-06-08 03:21:23',NULL,NULL,7),(67,'Reserva Pendiente','Tu reserva para Cancha Multideportiva (08:00 - 10:00 el 23/06/25) está pendiente de pago.','reserva',0,'2025-06-08 03:27:59',NULL,NULL,7),(68,'Reserva confirmada','Tu reserva para Cancha Multideportiva el día 2025-06-23 de 08:00 a 10:00 ha sido confirmada.','reserva',0,'2025-06-08 03:28:54','confirmacion',NULL,7),(72,'Reserva Pendiente','Tu reserva para Prueba (08:00 - 10:00 el 24/06/25) está pendiente de pago.','reserva',0,'2025-06-08 03:32:02',NULL,NULL,7),(76,'Reserva confirmada','Tu reserva para Prueba el día 2025-06-24 de 08:00 a 10:00 ha sido confirmada.','reserva',0,'2025-06-08 03:35:29','confirmacion',NULL,7),(96,'Reserva Pendiente','Tu reserva para Prueba (08:00 - 10:00 el 1/07/25) está pendiente de pago.','reserva',0,'2025-06-08 05:08:49',NULL,NULL,7),(97,'Reserva cancelada','Tu reserva para Prueba el día 2025-07-01 de 08:00 a 10:00 ha sido cancelada.','reserva',0,'2025-06-08 05:09:41','cancelacion',NULL,7),(98,'Reserva cancelada','La reserva #RES-15 ha sido cancelada.','reserva',0,'2025-06-08 05:09:41',NULL,NULL,3),(99,'Reserva Pendiente','Tu reserva para Prueba (08:00 - 10:00 el 22/07/25) está pendiente de pago.','reserva',0,'2025-06-08 05:30:40',NULL,NULL,7),(100,'Reserva cancelada','Tu reserva para Prueba el día 2025-07-22 de 08:00 a 10:00 ha sido cancelada.','reserva',0,'2025-06-08 05:30:53','cancelacion',NULL,7),(101,'Reserva Cancelada','Tu reserva para Prueba (08:00 - 10:00 el martes 22 de julio de 2025) ha sido cancelada.','reserva',0,'2025-06-08 05:30:53',NULL,NULL,7);
/*!40000 ALTER TABLE `notificaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `observaciones`
--

DROP TABLE IF EXISTS `observaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `observaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `usuario_id` int NOT NULL,
  `titulo` varchar(100) NOT NULL,
  `descripcion` text NOT NULL,
  `estado` enum('pendiente','en_proceso','resuelta','cancelada') DEFAULT 'pendiente',
  `prioridad` enum('baja','media','alta','urgente') DEFAULT 'media',
  `fecha_resolucion` datetime DEFAULT NULL,
  `resuelto_por` int DEFAULT NULL,
  `comentario_resolucion` text,
  `fotos_url` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `resuelto_por` (`resuelto_por`),
  CONSTRAINT `observaciones_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`),
  CONSTRAINT `observaciones_ibfk_2` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  CONSTRAINT `observaciones_ibfk_3` FOREIGN KEY (`resuelto_por`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `observaciones`
--

LOCK TABLES `observaciones` WRITE;
/*!40000 ALTER TABLE `observaciones` DISABLE KEYS */;
INSERT INTO `observaciones` VALUES (1,2,5,'se cayo un gato','miau','pendiente','media',NULL,4,NULL,NULL,'2025-04-28 15:56:20','2025-04-28 15:56:20'),(2,1,4,'Filtro de agua requiere mantenimiento','El sistema de filtración de la piscina no está funcionando correctamente','pendiente','alta',NULL,NULL,NULL,NULL,'2025-04-29 10:15:20','2025-04-29 10:15:20'),(3,1,4,'Azulejos rotos en el borde sur','Hay varios azulejos rotos que representan un peligro para los usuarios','en_proceso','media','2025-05-01 14:30:00',4,'En proceso de reparación',NULL,'2025-04-30 09:45:10','2025-05-01 14:30:00'),(4,2,5,'Daños en la red de la portería norte','La red está rota y necesita ser reemplazada','pendiente','media',NULL,NULL,NULL,NULL,'2025-05-01 11:25:15','2025-05-01 11:25:15'),(5,2,4,'Grass desgastado en el área central','El área central muestra signos de desgaste excesivo','pendiente','baja',NULL,NULL,NULL,NULL,'2025-05-02 13:10:30','2025-05-02 13:10:30'),(6,3,4,'Máquina de pesas necesita lubricación','La máquina de pecho hace ruido al ser utilizada','resuelta','media','2025-05-03 16:45:00',4,'Se aplicó lubricante y ahora funciona correctamente',NULL,'2025-05-03 08:30:45','2025-05-03 16:45:00'),(7,3,5,'Fuga de agua en los vestidores','Hay una fuga en la tubería del baño de hombres','pendiente','alta',NULL,NULL,NULL,NULL,'2025-05-04 07:55:20','2025-05-04 07:55:20');
/*!40000 ALTER TABLE `observaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pagos`
--

DROP TABLE IF EXISTS `pagos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pagos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `reserva_id` int NOT NULL,
  `monto` decimal(10,2) NOT NULL,
  `metodo` enum('online','deposito') NOT NULL,
  `estado` enum('pendiente','pagado','fallido','reembolsado') DEFAULT 'pendiente',
  `referencia_transaccion` varchar(100) DEFAULT NULL,
  `url_comprobante` varchar(255) DEFAULT NULL,
  `ultimos_digitos` varchar(4) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `reserva_id` (`reserva_id`),
  CONSTRAINT `pagos_ibfk_1` FOREIGN KEY (`reserva_id`) REFERENCES `reservas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pagos`
--

LOCK TABLES `pagos` WRITE;
/*!40000 ALTER TABLE `pagos` DISABLE KEYS */;
INSERT INTO `pagos` VALUES (1,1,55.00,'deposito','pendiente',NULL,'/comprobantes/reserva1_comprobante.jpg',NULL,'2025-05-21 03:33:21','2025-05-21 03:33:21'),(2,2,35.00,'online','pendiente','TRX-23456789',NULL,'4567','2025-05-21 03:33:21','2025-05-21 03:33:21'),(3,3,25.00,'online','pagado','TRX-34567890',NULL,'5678','2025-05-18 03:33:21','2025-05-18 03:33:21'),(4,4,45.00,'online','pagado','TRX-45678901',NULL,'6789','2025-05-19 03:33:21','2025-05-19 03:33:21'),(5,5,30.00,'deposito','pagado','DEP-56789012','/comprobantes/reserva5_comprobante.jpg',NULL,'2025-05-16 03:33:21','2025-05-17 03:33:21'),(6,6,35.00,'online','pagado','TRX-67890123',NULL,'7890','2025-05-01 10:30:00','2025-05-01 10:35:00'),(7,7,55.00,'deposito','pagado','DEP-78901234','/comprobantes/reserva7_comprobante.jpg',NULL,'2025-05-02 15:00:00','2025-05-02 17:30:00'),(8,11,22.00,'deposito','reembolsado',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_11_1749347991489.jpeg',NULL,'2025-06-08 01:59:52','2025-06-08 03:21:23'),(9,12,22.00,'deposito','reembolsado',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_12_1749350125808.pdf',NULL,'2025-06-08 02:35:26','2025-06-08 02:37:42'),(10,13,160.00,'deposito','pagado',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_13_1749353278334.pdf',NULL,'2025-06-08 03:27:59','2025-06-08 03:28:54'),(11,14,22.00,'deposito','pagado',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_14_1749353521754.pdf',NULL,'2025-06-08 03:32:02','2025-06-08 03:35:29'),(12,15,22.00,'deposito','fallido',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_15_1749359327743.pdf',NULL,'2025-06-08 05:08:49','2025-06-08 05:09:41'),(13,16,22.00,'deposito','reembolsado',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/comprobantes/comprobante_reserva_16_1749360639041.pdf',NULL,'2025-06-08 05:30:40','2025-06-08 05:30:53');
/*!40000 ALTER TABLE `pagos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `token` varchar(255) NOT NULL,
  `usuario_id` int NOT NULL,
  `expiracion` timestamp NOT NULL,
  `usado` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_password_reset_token` (`token`),
  KEY `idx_password_reset_usuario` (`usuario_id`),
  CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

LOCK TABLES `password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `preferencias_notificaciones`
--

DROP TABLE IF EXISTS `preferencias_notificaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `preferencias_notificaciones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `usuario_id` int NOT NULL,
  `email` tinyint(1) DEFAULT '1',
  `reservas` tinyint(1) DEFAULT '1',
  `promociones` tinyint(1) DEFAULT '0',
  `mantenimiento` tinyint(1) DEFAULT '1',
  `frecuencia` enum('tiempo-real','diario','semanal') DEFAULT 'tiempo-real',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `preferencias_notificaciones_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `preferencias_notificaciones`
--

LOCK TABLES `preferencias_notificaciones` WRITE;
/*!40000 ALTER TABLE `preferencias_notificaciones` DISABLE KEYS */;
/*!40000 ALTER TABLE `preferencias_notificaciones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reglas_instalacion`
--

DROP TABLE IF EXISTS `reglas_instalacion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reglas_instalacion` (
  `id` int NOT NULL AUTO_INCREMENT,
  `instalacion_id` int NOT NULL,
  `descripcion` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `reglas_instalacion_ibfk_1` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reglas_instalacion`
--

LOCK TABLES `reglas_instalacion` WRITE;
/*!40000 ALTER TABLE `reglas_instalacion` DISABLE KEYS */;
INSERT INTO `reglas_instalacion` VALUES (1,1,'Uso obligatorio de gorro de baño','2025-05-21 03:33:21','2025-05-21 03:33:21'),(2,1,'Ducharse antes de ingresar','2025-05-21 03:33:21','2025-05-21 03:33:21'),(3,1,'No consumir alimentos en el área de piscina','2025-05-21 03:33:21','2025-05-21 03:33:21'),(4,2,'Prohibido el uso de tacos metálicos','2025-05-21 03:33:21','2025-05-21 03:33:21'),(5,2,'No ingresar con alimentos','2025-05-21 03:33:21','2025-05-21 03:33:21'),(6,2,'Respetar horarios asignados','2025-05-21 03:33:21','2025-05-21 03:33:21'),(7,6,'x','2025-06-08 00:34:11','2025-06-08 00:34:11');
/*!40000 ALTER TABLE `reglas_instalacion` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reportes`
--

DROP TABLE IF EXISTS `reportes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reportes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  `tipo` varchar(50) NOT NULL,
  `formato` varchar(20) NOT NULL,
  `rango_fechas` varchar(50) NOT NULL,
  `fecha_creacion` datetime NOT NULL,
  `usuario_id` int NOT NULL,
  `tamano` varchar(20) DEFAULT NULL,
  `descripcion` text,
  `instalacion_id` int DEFAULT NULL,
  `url_archivo` varchar(500) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `reportes_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  CONSTRAINT `reportes_ibfk_2` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reportes`
--

LOCK TABLES `reportes` WRITE;
/*!40000 ALTER TABLE `reportes` DISABLE KEYS */;
INSERT INTO `reportes` VALUES (50,'Reporte de Uso de Instalaciones','instalaciones','pdf','2024-06-07 - 2025-06-07','2025-06-07 22:55:46',3,'97,1 KB','Métricas de utilización: frecuencia, horarios más solicitados, capacidad',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_instalaciones_1749354944166.pdf'),(51,'Reporte de Asistencias','asistencias','pdf','2025-05-08 - 2025-06-07','2025-06-07 23:26:32',3,'145,3 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749356790262.pdf'),(52,'Reporte de Asistencias','asistencias','pdf','2024-06-07 - 2026-06-07','2025-06-07 23:29:49',3,'193,8 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749356987841.pdf'),(53,'Reporte de Asistencias','asistencias','pdf','2024-06-07 - 2026-06-07','2025-06-07 23:31:55',3,'48,8 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357113839.pdf'),(54,'Reporte de Asistencias','asistencias','pdf','2025-06-04 - 2025-06-26','2025-06-07 23:32:09',3,'48,8 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357128526.pdf'),(55,'Reporte de Asistencias','asistencias','pdf','2024-06-07 - 2026-06-07','2025-06-07 23:33:44',3,'145,3 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357223030.pdf'),(56,'Reporte de Asistencias','asistencias','pdf','2024-06-07 - 2026-06-07','2025-06-07 23:33:58',3,'48,8 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357237602.pdf'),(57,'Reporte de Asistencias','asistencias','excel','2024-06-07 - 2026-06-07','2025-06-07 23:34:29',3,'4,2 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357268516.xlsx'),(58,'Reporte de Asistencias','asistencias','excel','2024-06-07 - 2026-06-07','2025-06-07 23:34:49',3,'3,8 KB','Historial de asistencias de coordinadores',NULL,'https://goajrdpkfhunnfuqtoub.supabase.co/storage/v1/object/public/reportes/reporte_asistencias_1749357289206.xlsx');
/*!40000 ALTER TABLE `reportes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `reservas`
--

DROP TABLE IF EXISTS `reservas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reservas` (
  `id` int NOT NULL AUTO_INCREMENT,
  `usuario_id` int NOT NULL,
  `instalacion_id` int NOT NULL,
  `fecha` date NOT NULL,
  `hora_inicio` time NOT NULL,
  `hora_fin` time NOT NULL,
  `estado` enum('pendiente','confirmada','cancelada','completada') DEFAULT 'pendiente',
  `comentarios` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `estado_pago` enum('pendiente','pagado','fallido','reembolsado') DEFAULT 'pendiente',
  `metodo_pago` enum('online','deposito') DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  KEY `instalacion_id` (`instalacion_id`),
  CONSTRAINT `reservas_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  CONSTRAINT `reservas_ibfk_2` FOREIGN KEY (`instalacion_id`) REFERENCES `instalaciones` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reservas`
--

LOCK TABLES `reservas` WRITE;
/*!40000 ALTER TABLE `reservas` DISABLE KEYS */;
INSERT INTO `reservas` VALUES (1,2,1,'2025-05-20','10:00:00','12:00:00','pendiente','Necesito acceso a carriles para nadar','2025-05-21 03:33:21','2025-05-21 03:33:21','pendiente','deposito'),(2,2,3,'2025-05-22','15:00:00','17:00:00','pendiente','Entrenamiento de fuerza','2025-05-21 03:33:21','2025-05-21 03:33:21','pendiente','online'),(3,2,5,'2025-05-25','08:00:00','10:00:00','confirmada','Partido de tenis dobles con amigos','2025-05-21 03:33:21','2025-05-21 03:33:21','pagado','online'),(4,2,2,'2025-05-28','16:00:00','18:00:00','confirmada','Encuentro entre equipos del barrio','2025-05-21 03:33:21','2025-05-21 03:33:21','pagado','online'),(5,2,4,'2025-06-01','09:00:00','11:00:00','confirmada','Práctica de tiros libres','2025-05-21 03:33:21','2025-05-21 03:33:21','pagado','deposito'),(6,2,3,'2025-05-01','14:00:00','16:00:00','completada','Entrenamiento completado satisfactoriamente','2025-05-01 10:00:00','2025-05-01 16:30:00','pagado','online'),(7,2,1,'2025-05-05','09:00:00','11:00:00','completada','Práctica de natación','2025-05-02 14:00:00','2025-05-05 11:30:00','pagado','deposito'),(8,2,5,'2025-05-10','15:00:00','17:00:00','completada','Partido amistoso','2025-05-07 09:00:00','2025-05-10 17:15:00','pagado','online'),(11,7,6,'2025-06-24','08:00:00','10:00:00','cancelada','','2025-06-08 01:59:51','2025-06-08 03:21:23','reembolsado','deposito'),(12,7,6,'2025-07-22','08:00:00','10:00:00','cancelada','','2025-06-08 02:35:26','2025-06-08 02:37:42','reembolsado','deposito'),(13,7,4,'2025-06-23','08:00:00','10:00:00','confirmada','','2025-06-08 03:27:58','2025-06-08 03:28:54','pagado','deposito'),(14,7,6,'2025-06-24','08:00:00','10:00:00','confirmada','','2025-06-08 03:32:02','2025-06-08 03:35:29','pagado','deposito'),(15,7,6,'2025-07-01','08:00:00','10:00:00','cancelada','','2025-06-08 05:08:48','2025-06-08 05:09:41','fallido','deposito'),(16,7,6,'2025-07-22','08:00:00','10:00:00','cancelada','','2025-06-08 05:30:39','2025-06-08 05:30:53','reembolsado','deposito');
/*!40000 ALTER TABLE `reservas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `descripcion` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nombre` (`nombre`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES (1,'superadmin','Administrador principal del sistema con acceso completo','2025-04-24 14:07:15','2025-04-24 14:07:15'),(2,'admin','Administrador con acceso a la gestión del sistema','2025-04-24 14:07:15','2025-04-24 14:07:15'),(3,'coordinador','Coordinador de instalaciones deportivas','2025-04-24 14:07:15','2025-04-24 14:07:15'),(4,'vecino','Usuario vecino que puede realizar reservas','2025-04-24 14:07:15','2025-04-24 14:07:15');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sesiones`
--

DROP TABLE IF EXISTS `sesiones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sesiones` (
  `id` int NOT NULL AUTO_INCREMENT,
  `usuario_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `device` varchar(100) DEFAULT NULL,
  `ubicacion` varchar(100) DEFAULT NULL,
  `fecha_inicio` datetime DEFAULT CURRENT_TIMESTAMP,
  `fecha_expiracion` datetime DEFAULT NULL,
  `activa` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `usuario_id` (`usuario_id`),
  CONSTRAINT `sesiones_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sesiones`
--

LOCK TABLES `sesiones` WRITE;
/*!40000 ALTER TABLE `sesiones` DISABLE KEYS */;
/*!40000 ALTER TABLE `sesiones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `usuarios`
--

DROP TABLE IF EXISTS `usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `usuarios` (
  `id` int NOT NULL AUTO_INCREMENT,
  `nombre` varchar(100) NOT NULL,
  `apellidos` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `telefono` varchar(20) DEFAULT NULL,
  `direccion` varchar(255) DEFAULT NULL,
  `dni` varchar(20) DEFAULT NULL,
  `role_id` int NOT NULL,
  `avatar_url` varchar(255) DEFAULT NULL,
  `activo` tinyint(1) DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `ip_address` varchar(50) DEFAULT NULL,
  `device` varchar(100) DEFAULT NULL,
  `fecha_expiracion_password` date DEFAULT NULL,
  `intentos_fallidos` int DEFAULT '0',
  `bloqueado_hasta` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `dni` (`dni`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `usuarios`
--

LOCK TABLES `usuarios` WRITE;
/*!40000 ALTER TABLE `usuarios` DISABLE KEYS */;
INSERT INTO `usuarios` VALUES (1,'Administrador','Principal','<EMAIL>','$2a$10$iPuhWsBohOQ5mesVSihxBO4PtMd7c1LwbTZNcfogwtxSkjtj9G8ba','987654321',NULL,NULL,1,NULL,1,NULL,NULL,NULL,NULL,0,NULL,'2025-04-24 14:07:15','2025-04-24 14:07:15'),(2,'Usuario','Vecino','<EMAIL>','$2a$10$iPuhWsBohOQ5mesVSihxBO4PtMd7c1LwbTZNcfogwtxSkjtj9G8ba','999999999','Av Aguarico 133','70901759',4,NULL,1,NULL,NULL,NULL,NULL,0,NULL,'2025-04-24 14:07:15','2025-04-24 14:07:15'),(3,'Usuario','Administrador','<EMAIL>','$2a$10$iPuhWsBohOQ5mesVSihxBO4PtMd7c1LwbTZNcfogwtxSkjtj9G8ba','988888888',NULL,NULL,2,NULL,1,NULL,NULL,NULL,NULL,0,NULL,'2025-04-24 14:07:15','2025-04-24 14:07:15'),(4,'juan','huaman','<EMAIL>','$2a$10$iPuhWsBohOQ5mesVSihxBO4PtMd7c1LwbTZNcfogwtxSkjtj9G8ba','977777777',NULL,NULL,3,NULL,1,NULL,NULL,NULL,NULL,0,NULL,'2025-04-24 14:07:15','2025-04-28 14:08:37'),(5,'gato','rron rron','<EMAIL>','$2a$10$iPuhWsBohOQ5mesVSihxBO4PtMd7c1LwbTZNcfogwtxSkjtj9G8ba','12343432',NULL,NULL,3,NULL,1,NULL,NULL,NULL,NULL,0,NULL,'2025-04-28 14:45:15','2025-04-29 01:00:08'),(7,'Gerardo Jose','Rabanal Callirgos','<EMAIL>','$2a$10$zTWfZK1BrFWhkMs9Wrpm.uasJLb6RycUhMwjwfPRbdOFduvNkAw56','960150297','Jr. Aguarico 666 Dpto 304 A','12345678',4,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,'2025-05-21 03:36:18','2025-05-21 03:59:17'),(8,'Joaquin','Rabanal','<EMAIL>','$2a$10$a/aZaw19DyyHy2LLhEXXFu6muCdRGNQAvr/6GkJXJLUMIszgfMgvS','111222333','Av Ejemplo 134',NULL,3,NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-08 00:37:07','2025-06-08 00:37:07');
/*!40000 ALTER TABLE `usuarios` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-08  1:02:14
