# Sistema de Geolocalización - DeporSM

## Resumen de Implementación

Se ha implementado un sistema completo de geolocalización para la aplicación DeporSM que incluye:

1. **Geocodificación automática** en formularios de creación de instalaciones
2. **Mapa interactivo** para coordinadores con sus instalaciones asignadas
3. **Validación de ubicación** para registro de asistencias y observaciones (✅ IMPLEMENTADO EN OBSERVACIONES)
4. **Integración con Google Maps API**

## ✅ Estado de Implementación

### Completado:
- ✅ Base de datos actualizada con campos de coordenadas
- ✅ Backend modificado para soportar coordenadas
- ✅ Mapa interactivo para coordinadores mejorado
- ✅ Componente LocationValidator creado
- ✅ Validación de ubicación integrada en formulario de observaciones
- ✅ Utilidades de Google Maps implementadas

### Pendiente:
- ⏳ Integrar validación en registro de asistencias
- ⏳ Agregar geocodificación al formulario de crear instalaciones (admin)

## Configuración Requerida

### 1. Base de Datos
Ejecutar el script SQL para agregar campos de coordenadas:
```sql
-- Ejecutar add_coordinates_to_instalaciones.sql
ALTER TABLE `instalaciones`
ADD COLUMN `latitud` DECIMAL(10, 8) NULL,
ADD COLUMN `longitud` DECIMAL(11, 8) NULL,
ADD COLUMN `radio_validacion` INT DEFAULT 100;
```

### 2. Variables de Entorno
Agregar a tu archivo `.env.local`:
```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=tu_api_key_de_google_maps
```

### 3. Google Maps API
Asegúrate de que tu API Key tenga habilitadas estas APIs:
- Maps JavaScript API
- Geocoding API
- Places API (opcional)

## Archivos Creados/Modificados

### Frontend - Nuevos Archivos:
- `lib/google-maps.ts` - Utilidades para Google Maps
- `components/maps/FacilitiesMap.tsx` - Componente de mapa
- `components/location/LocationValidator.tsx` - Validador de ubicación
- `components/forms/AddressGeocoder.tsx` - Geocodificador de direcciones
- `app/coordinador/instalaciones/mapa/page_new.tsx` - Nueva página de mapa

### Backend - Archivos Modificados:
- `model/Instalacion.java` - Agregados campos de coordenadas
- `dto/InstalacionRequestDTO.java` - Agregados campos de coordenadas
- `dto/InstalacionDetalleDTO.java` - Agregados campos de coordenadas
- `controller/InstalacionesController.java` - Soporte para coordenadas

### Scripts SQL:
- `add_coordinates_to_instalaciones.sql` - Migración de base de datos
- `update_instalaciones_images.sql` - Actualización de imágenes locales

## Funcionalidades Implementadas

### 1. Mapa de Instalaciones para Coordinadores
- **Ubicación**: `/coordinador/instalaciones/mapa`
- **Funcionalidad**:
  - Muestra instalaciones asignadas al coordinador
  - Círculos de validación para cada instalación
  - Geolocalización del usuario
  - Información detallada de cada instalación

### 2. Geocodificación en Formularios
- **Componente**: `AddressGeocoder`
- **Funcionalidad**:
  - Convierte direcciones en coordenadas automáticamente
  - Validación de direcciones
  - Vista previa en Google Maps

### 3. Validación de Ubicación
- **Componente**: `LocationValidator`
- **Funcionalidad**:
  - Verifica que el usuario esté dentro del radio permitido
  - Cálculo de distancia en tiempo real
  - Mensajes de error descriptivos

## Uso en Observaciones y Asistencias

### Para Observaciones:
```tsx
import LocationValidator from "@/components/location/LocationValidator"

// En el formulario de observación
<LocationValidator
  facilityLocation={{ lat: instalacion.latitud, lng: instalacion.longitud }}
  facilityName={instalacion.nombre}
  allowedRadius={instalacion.radioValidacion}
  onValidationResult={(isValid, distance) => {
    setLocationValid(isValid)
    // Solo permitir envío si la ubicación es válida
  }}
/>
```

### Para Registro de Asistencia:
```tsx
// Similar implementación en el formulario de asistencia
// Validar ubicación antes de permitir el registro
```

## Configuración de Coordenadas

### Coordenadas por Defecto (San Miguel, Lima):
- **Piscina Olímpica**: -12.0776, -77.0919 (Radio: 150m)
- **Cancha de Fútbol**: -12.0756, -77.0889 (Radio: 200m)
- **Gimnasio Municipal**: -12.0796, -77.0939 (Radio: 100m)
- **Cancha Multideportiva**: -12.0736, -77.0869 (Radio: 120m)
- **Pista de Atletismo**: -12.0786, -77.0909 (Radio: 180m)

## Próximos Pasos

1. **Integrar validación de ubicación** en formularios de observaciones
2. **Implementar validación** en registro de asistencias
3. **Agregar geocodificación** al formulario de crear instalaciones
4. **Optimizar rendimiento** del mapa para muchas instalaciones
5. **Agregar notificaciones** cuando el coordinador esté fuera del radio

## Consideraciones de Seguridad

- Las coordenadas se validan en el backend
- La API Key de Google Maps está restringida por dominio
- La geolocalización requiere permisos del usuario
- Los radios de validación son configurables por instalación

## Troubleshooting

### Error: "Google Maps API Key no configurada"
- Verificar que `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` esté en `.env.local`
- Reiniciar el servidor de desarrollo

### Error: "Geolocalización no soportada"
- Verificar que el navegador soporte geolocalización
- Usar HTTPS en producción (requerido para geolocalización)

### Error: "Ubicación inválida"
- Verificar que las coordenadas de la instalación sean correctas
- Ajustar el radio de validación si es necesario
- Verificar que el GPS del dispositivo esté activado
