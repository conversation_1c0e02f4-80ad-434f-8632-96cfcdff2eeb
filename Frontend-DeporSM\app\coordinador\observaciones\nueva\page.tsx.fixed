"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Upload, Loader2, CheckCircle, AlertCircle, MapPin } from "lucide-react"
import Link from "next/link"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

// Datos de ejemplo para las instalaciones
const facilitiesData = [
  { id: 1, name: "Cancha de Futbol (Grass)" },
  { id: 2, name: "Piscina Municipal" },
]

export default function NuevaObservacion() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const facilityId = searchParams.get("id");

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formError, setFormError] = useState("");
  const [photos, setPhotos] = useState<File[]>([]);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [isLocationValid, setIsLocationValid] = useState(false);
  const [isCheckingLocation, setIsCheckingLocation] = useState(false);

  // Estado del formulario
  const [formData, setFormData] = useState({
    facilityId: "",
    description: "",
    priority: "media",
  });

  // Estado para errores de validación
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    // Simulación de carga de datos
    const loadData = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Si hay un ID de instalación en la URL, preseleccionarlo
      if (facilityId) {
        setFormData((prev) => ({
          ...prev,
          facilityId: facilityId,
        }));
      }
      
      setIsLoading(false);
    };
    
    loadData();
  }, [facilityId]);
  
  const checkUserLocation = () => {
    setIsCheckingLocation(true);

    // Simulamos un pequeño retraso para que parezca que está verificando
    setTimeout(() => {
      // En modo de prueba, siempre establecemos la ubicación como válida
      setUserLocation({
        lat: -12.077, // Coordenadas de prueba (Cancha de Futbol)
        lng: -77.083,
      });
      
      // Marcar la ubicación como válida
      setIsLocationValid(true);
      
      // Eliminar cualquier error de ubicación que pueda existir
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.location;
        return newErrors;
      });
      
      // Finalizar verificación
      setIsCheckingLocation(false);
    }, 1000);
    
    // El código original está comentado para futuras referencias
    /*
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLoc = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setUserLocation(userLoc);

          // Simulación de validación de ubicación
          const selectedFacilityId = Number(formData.facilityId);
          const facilityLocations = {
            1: { lat: -12.077, lng: -77.083 },
            2: { lat: -12.079, lng: -77.085 },
          };
          
          const facilityLoc = facilityLocations[selectedFacilityId];
          if (facilityLoc) {
            const distance = Math.sqrt(
              Math.pow(userLoc.lat - facilityLoc.lat, 2) + Math.pow(userLoc.lng - facilityLoc.lng, 2)
            );
            const isValid = distance < 0.001;
            setIsLocationValid(isValid);
            
            if (!isValid) {
              setErrors((prev) => ({
                ...prev,
                location: "Debes estar físicamente en la instalación para reportar una observación"
              }));
            } else {
              setErrors((prev) => {
                const newErrors = { ...prev };
                delete newErrors.location;
                return newErrors;
              });
            }
          }
          setIsCheckingLocation(false);
        },
        (error) => {
          console.error("Error obteniendo la ubicación:", error);
          setErrors((prev) => ({
            ...prev,
            location: "No se pudo obtener tu ubicación. Por favor, habilita los permisos de ubicación."
          }));
          setIsCheckingLocation(false);
        }
      );
    } else {
      setErrors((prev) => ({
        ...prev,
        location: "Tu navegador no soporta geolocalización"
      }));
      setIsCheckingLocation(false);
    }
    */
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Limpiar error al editar
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Limpiar error al editar
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // Si se cambia la instalación, resetear la validación de ubicación
    if (name === "facilityId") {
      setIsLocationValid(false);
      setUserLocation(null);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newPhotos = Array.from(e.target.files);

      // Validar tipo de archivo
      const invalidFiles = newPhotos.filter((file) => !["image/jpeg", "image/png", "image/webp"].includes(file.type));
      if (invalidFiles.length > 0) {
        setErrors((prev) => ({
          ...prev,
          photos: "Solo se permiten archivos JPG, PNG o WEBP",
        }));
        return;
      }

      // Validar tamaño (máximo 5MB por archivo)
      const oversizedFiles = newPhotos.filter((file) => file.size > 5 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        setErrors((prev) => ({
          ...prev,
          photos: "Cada archivo no debe superar los 5MB",
        }));
        return;
      }

      // Validar cantidad máxima (3 fotos)
      if (photos.length + newPhotos.length > 3) {
        setErrors((prev) => ({
          ...prev,
          photos: "Máximo 3 fotos permitidas",
        }));
        return;
      }

      setPhotos((prev) => [...prev, ...newPhotos]);

      // Limpiar error si existe
      if (errors.photos) {
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.photos;
          return newErrors;
        });
      }
    }
  };

  const removePhoto = (index: number) => {
    setPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.facilityId) {
      newErrors.facilityId = "Debes seleccionar una instalación";
    }

    if (!formData.description.trim()) {
      newErrors.description = "La descripción es obligatoria";
    }
    
    if (!formData.priority) {
      newErrors.priority = "Debes seleccionar una prioridad";
    }

    // Temporalmente desactivada la validación de ubicación para pruebas
    // if (!isLocationValid) {
    //   newErrors.location = "Debes verificar tu ubicación antes de enviar la observación"
    // }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      setFormError("Por favor, corrige los errores en el formulario antes de continuar.");
      return;
    }

    setIsSubmitting(true);
    setFormError("");

    try {
      // Preparar los datos para la API
      // Primero subir las fotos a algún servicio de almacenamiento (simulamos URLs)
      const fotoUrls = photos.map(photo => URL.createObjectURL(photo));
      
      const observacionData = {
        usuarioId: 4, // Aquí deberías obtener el ID del usuario autenticado
        instalacionId: parseInt(formData.facilityId),
        titulo: "Observación de instalación", // Título por defecto
        descripcion: formData.description,
        prioridad: formData.priority,
        ubicacionLat: userLocation?.lat.toString(),
        ubicacionLng: userLocation?.lng.toString(),
        fotos: fotoUrls // Agregar las URLs de las fotos
      };

      // Llamada a la API para crear la observación
      const response = await fetch('${API_BASE_URL}/observaciones', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(observacionData),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const responseData = await response.json();
      console.log("Observación creada:", responseData);

      setIsSuccess(true);

      // Redireccionar después de 2 segundos
      setTimeout(() => {
        router.push("/coordinador/observaciones");
      }, 2000);
    } catch (error) {
      console.error("Error al crear la observación:", error);
      setFormError("Ocurrió un error al crear la observación. Por favor, intenta nuevamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" className="mr-2" asChild>
          <Link href="/coordinador/observaciones">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Nueva Observación</h1>
      </div>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Reportar Observación</CardTitle>
            <CardDescription>Ingresa los detalles de la observación para la instalación</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Información básica */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="facilityId">
                  Instalación <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.facilityId}
                  onValueChange={(value) => handleSelectChange("facilityId", value)}
                  disabled={isLocationValid}
                >
                  <SelectTrigger id="facilityId" className={errors.facilityId ? "border-red-500" : ""}>
                    <SelectValue placeholder="Selecciona una instalación" />
                  </SelectTrigger>
                  <SelectContent>
                    {facilitiesData.map((facility) => (
                      <SelectItem key={facility.id} value={facility.id.toString()}>
                        {facility.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.facilityId && <p className="text-red-500 text-sm">{errors.facilityId}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">
                  Verificación de ubicación <span className="text-red-500">*</span>
                </Label>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={checkUserLocation}
                    disabled={isCheckingLocation || isLocationValid}
                    className="flex-1"
                  >
                    {isCheckingLocation ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verificando...
                      </>
                    ) : isLocationValid ? (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                        Ubicación verificada
                      </>
                    ) : (
                      <>
                        <MapPin className="mr-2 h-4 w-4" />
                        Verificar mi ubicación
                      </>
                    )}
                  </Button>
                  {isLocationValid && <Badge className="bg-green-100 text-green-800">Ubicación válida</Badge>}
                </div>
                {errors.location && <p className="text-red-500 text-sm">{errors.location}</p>}
                <p className="text-xs text-gray-500">
                  Debes estar físicamente en la instalación para reportar una observación
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">
                  Descripción <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Describe detalladamente la observación"
                  value={formData.description}
                  onChange={handleInputChange}
                  className={errors.description ? "border-red-500" : ""}
                  rows={4}
                />
                {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">
                  Prioridad <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.priority} onValueChange={(value) => handleSelectChange("priority", value)}>
                  <SelectTrigger id="priority" className={errors.priority ? "border-red-500" : ""}>
                    <SelectValue placeholder="Selecciona una prioridad" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="alta">Alta</SelectItem>
                    <SelectItem value="media">Media</SelectItem>
                    <SelectItem value="baja">Baja</SelectItem>
                  </SelectContent>
                </Select>
                {errors.priority && <p className="text-red-500 text-sm">{errors.priority}</p>}
              </div>
            </div>

            {/* Fotos */}
            <div className="space-y-2">
              <Label htmlFor="photos">Fotos (opcional)</Label>
              <div
                className={`border-2 border-dashed rounded-md p-6 ${errors.photos ? "border-red-500" : "border-gray-300"}`}
              >
                <div className="space-y-4">
                  {photos.length > 0 ? (
                    <div className="grid grid-cols-3 gap-4">
                      {photos.map((photo, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(photo) || "/placeholder.svg"}
                            alt={`Foto ${index + 1}`}
                            className="w-full h-24 object-cover rounded-md"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                            onClick={() => removePhoto(index)}
                          >
                            &times;
                          </Button>
                        </div>
                      ))}
                      {photos.length < 3 && (
                        <div
                          className="border-2 border-dashed border-gray-300 rounded-md h-24 flex items-center justify-center cursor-pointer"
                          onClick={() => document.getElementById("photos")?.click()}
                        >
                          <Upload className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center">
                      <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="text-sm text-gray-600 mt-2">Haz clic para seleccionar o arrastra y suelta fotos</p>
                      <Button
                        type="button"
                        variant="outline"
                        className="mt-2"
                        onClick={() => document.getElementById("photos")?.click()}
                      >
                        Seleccionar fotos
                      </Button>
                    </div>
                  )}
                  <Input
                    id="photos"
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    className="hidden"
                    onChange={handleFileChange}
                    multiple
                  />
                </div>
              </div>
              {errors.photos && <p className="text-red-500 text-sm">{errors.photos}</p>}
              <p className="text-xs text-gray-500">
                Formatos aceptados: JPG, PNG, WEBP. Máximo 3 fotos. Tamaño máximo por foto: 5MB
              </p>
            </div>

            {formError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError}</AlertDescription>
              </Alert>
            )}

            {isSuccess && (
              <Alert className="bg-green-50 text-green-800 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription>Observación creada correctamente. Redirigiendo...</AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter className="flex justify-end space-x-4">
            <Button variant="outline" type="button" asChild>
              <Link href="/coordinador/observaciones">Cancelar</Link>
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-light" disabled={isSubmitting || isSuccess}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                "Enviar Observación"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
