-- Script para agregar campos de coordenadas a la tabla instalaciones
-- Esto permitirá la geolocalización y validación de ubicación

-- Agregar columnas de latitud y longitud
ALTER TABLE `instalaciones`
ADD COLUMN `latitud` DOUBLE NULL AFTER `ubicacion`,
ADD COLUMN `longitud` DOUBLE NULL AFTER `latitud`,
ADD COLUMN `radio_validacion` INT DEFAULT 100 AFTER `longitud` COMMENT 'Radio en metros para validación de ubicación';

-- Actualizar coordenadas para las instalaciones existentes (San Miguel, Lima)
-- Estas son coordenadas aproximadas del distrito de San Miguel

-- Piscina Olímpica - Complejo Deportivo Municipal
UPDATE `instalaciones` SET
    `latitud` = -12.0776,
    `longitud` = -77.0919,
    `radio_validacion` = 150
WHERE `id` = 1;

-- Cancha de Fútbol Principal - Estadio Municipal
UPDATE `instalaciones` SET
    `latitud` = -12.0756,
    `longitud` = -77.0889,
    `radio_validacion` = 200
WHERE `id` = 2;

-- Gimnasio Municipal - Centro Deportivo San Miguel
UPDATE `instalaciones` SET
    `latitud` = -12.0796,
    `longitud` = -77.0939,
    `radio_validacion` = 100
WHERE `id` = 3;

-- Cancha Multideportiva - Parque Deportivo Las Flores
UPDATE `instalaciones` SET
    `latitud` = -12.0736,
    `longitud` = -77.0869,
    `radio_validacion` = 120
WHERE `id` = 4;

-- Pista de Atletismo - Complejo Deportivo Municipal
UPDATE `instalaciones` SET
    `latitud` = -12.0786,
    `longitud` = -77.0909,
    `radio_validacion` = 180
WHERE `id` = 5;

-- Verificar los cambios
SELECT id, nombre, ubicacion, latitud, longitud, radio_validacion FROM `instalaciones` ORDER BY id;
