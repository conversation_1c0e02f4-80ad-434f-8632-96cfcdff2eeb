spring.application.name=deporsm

# Perfil activo (dev, prod)
spring.profiles.active=dev

spring.datasource.url=***********************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

spring.jpa.hibernate.ddl-auto=none

spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

spring.jpa.properties.hibernate.format_sql=true

spring.datasource.hikari.connection-timeout=20000

# Configuración de zona horaria para Perú (GMT-5)
spring.jpa.properties.hibernate.jdbc.time_zone=America/Lima

# Configuración de logs para Spring Security (depuración)
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.security.web.FilterChainProxy=DEBUG

# Configuración de sesiones
server.servlet.session.timeout=24h
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.same-site=lax
server.servlet.session.cookie.path=/
server.servlet.session.cookie.name=JSESSIONID
server.servlet.session.cookie.max-age=86400

# Desactivar Spring Session (usamos la gestión de sesiones integrada de Tomcat)
spring.session.store-type=none

# Solo crear sesión cuando sea necesario
server.servlet.session.tracking-modes=cookie

# Configuración CORS para desarrollo local
app.cors.allowed-origins=http://localhost:3000

# Configuración API RENIEC
reniec.api.url=https://api.apis.net.pe/v2/reniec/dni
reniec.api.token=apis-token-15398.doRo0MABSG9hVtdfYeIsD69fVOZkp4KV

